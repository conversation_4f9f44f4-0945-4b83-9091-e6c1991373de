package main

import (
	"fmt"
	"log"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/volume"
	"go.uber.org/zap"
)

func main() {
	// Initialize the application
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	fmt.Println("=== Testing Volume Update Task ===")

	// Create volume update task
	volumeTask := volume.NewVolumeUpdateTask()

	// Check current state before running
	fmt.Println("\n1. Checking current processing state...")
	var state model.VolumeProcessingState
	err := global.GVA_DB.Where("processor_name = ?", volume.ProcessorName15Min).First(&state).Error
	if err != nil {
		fmt.Printf("No existing processing state found (expected for first run): %v\n", err)
	} else {
		fmt.Printf("Current state: Last processed at %v, Last run at %v, Processed transactions: %d\n",
			state.LastProcessedAt, state.LastRunAt, state.ProcessedTransactions)
	}

	// Check some sample user tier info before update
	fmt.Println("\n2. Checking sample user tier info before update...")
	var userTierInfos []model.UserTierInfo
	err = global.GVA_DB.Limit(5).Find(&userTierInfos).Error
	if err != nil {
		log.Printf("Error getting user tier info: %v", err)
	} else {
		fmt.Printf("Found %d user tier info records\n", len(userTierInfos))
		for _, info := range userTierInfos {
			fmt.Printf("  User %s: Current volume = %s\n", info.UserID.String(), info.TradingVolumeUSD.String())
		}
	}

	// Check recent transactions
	fmt.Println("\n3. Checking recent completed transactions...")
	var recentTransactions []model.AffiliateTransaction
	err = global.GVA_DB.Where("status = ?", "Completed").
		Where("created_at > ?", time.Now().UTC().Add(-24*time.Hour)).
		Order("created_at DESC").
		Limit(10).
		Find(&recentTransactions).Error
	if err != nil {
		log.Printf("Error getting recent transactions: %v", err)
	} else {
		fmt.Printf("Found %d recent completed transactions in last 24 hours\n", len(recentTransactions))
		for _, tx := range recentTransactions {
			fmt.Printf("  TX %s: User %s, Volume %s USD, Created at %v\n",
				tx.TxHash, tx.UserID.String(), tx.VolumeUSD.String(), tx.CreatedAt)
		}
	}

	// Run the volume update task
	fmt.Println("\n4. Running volume update task...")
	startTime := time.Now()
	volumeTask.UpdateTradingVolumes()
	duration := time.Since(startTime)
	fmt.Printf("Task completed in %v\n", duration)

	// Check state after running
	fmt.Println("\n5. Checking processing state after update...")
	err = global.GVA_DB.Where("processor_name = ?", volume.ProcessorName15Min).First(&state).Error
	if err != nil {
		log.Printf("Error getting processing state: %v", err)
	} else {
		fmt.Printf("Updated state: Last processed at %v, Last run at %v, Processed transactions: %d\n",
			state.LastProcessedAt, state.LastRunAt, state.ProcessedTransactions)
	}

	// Check update logs
	fmt.Println("\n6. Checking volume update logs...")
	var updateLogs []model.UserVolumeUpdateLog
	err = global.GVA_DB.Where("processor_name = ?", volume.ProcessorName15Min).
		Order("processed_at DESC").
		Limit(10).
		Find(&updateLogs).Error
	if err != nil {
		log.Printf("Error getting update logs: %v", err)
	} else {
		fmt.Printf("Found %d update log entries\n", len(updateLogs))
		for _, log := range updateLogs {
			fmt.Printf("  User %s: Previous %s + Added %s = New %s (Transactions: %d)\n",
				log.UserID.String(),
				log.PreviousVolumeUSD.String(),
				log.AddedVolumeUSD.String(),
				log.NewVolumeUSD.String(),
				log.TransactionCount)
		}
	}

	// Check user tier info after update
	fmt.Println("\n7. Checking user tier info after update...")
	err = global.GVA_DB.Limit(5).Find(&userTierInfos).Error
	if err != nil {
		log.Printf("Error getting user tier info: %v", err)
	} else {
		fmt.Printf("Found %d user tier info records after update\n", len(userTierInfos))
		for _, info := range userTierInfos {
			fmt.Printf("  User %s: Current volume = %s\n", info.UserID.String(), info.TradingVolumeUSD.String())
		}
	}

	fmt.Println("\n=== Test completed ===")
}
