# Testing Configuration for Realtime Volume Sync
# Copy this to config.yaml for testing different intervals

# Database configuration
pgsql:
  path: {{ index . "DB_HOST" | default "127.0.0.1" }}:{{ index . "DB_PORT" | default "5432" }}
  port: {{ index . "DB_PORT" | default "5432" }}
  config: sslmode=disable TimeZone=UTC
  db-name: {{ index . "DB_NAME" | default "agent" }}
  username: {{ index . "DB_USER" | default "postgres" }}
  password: {{ index . "DB_PASSWORD" | default "postgres" }}
  prefix: ""
  singular: false
  engine: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: {{ index . "DB_LOG_MODE" | default "silent" }}
  log-zap: false

# System configuration
system:
  env: {{ index . "APP_ENV" | default "local" }}
  addr: {{ index . "SERVER_PORT" | default "8080" }}
  db-type: pgsql
  oss-type: local
  use-multipoint: false
  use-redis: {{ index . "REDIS_HOST" | ne "" }}
  iplimit-count: {{ index . "RATE_LIMIT_COUNT" | default "15000" }}
  iplimit-time: {{ index . "RATE_LIMIT_TIME" | default "3600" }}

# JWT configuration
jwt:
  signing-key: {{ index . "JWT_SIGNING_KEY" | default "xbit-agent-jwt-key" }}
  expires-time: {{ index . "JWT_EXPIRES_TIME" | default "7d" }}
  buffer-time: {{ index . "JWT_BUFFER_TIME" | default "1d" }}
  issuer: {{ index . "JWT_ISSUER" | default "xbit-agent" }}

# Redis configuration
redis:
  addr: {{ index . "REDIS_HOST" | default "127.0.0.1" }}:{{ index . "REDIS_PORT" | default "6379" }}
  password: {{ index . "REDIS_PASSWORD" | default "" }}
  db: {{ index . "REDIS_DB" | default "0" }}

# NATS configuration
nats:
  url: {{ index . "NATS_URL" | default "nats://127.0.0.1:4222" }}
  stream: {{ index . "NATS_STREAM" | default "AGENT" }}
  consumer: {{ index . "NATS_CONSUMER" | default "agent-consumer" }}

# Cron Tasks Configuration
# ========================
# TESTING CONFIGURATIONS - Choose one based on your testing needs:

# 🚀 RAPID TESTING (10 seconds) - For development only
# cron-tasks:
#   - id: "realtime_volume_sync"
#     cron: "*/10 * * * * *"  # Every 10 seconds - VERY FAST!

# ⚡ FAST TESTING (30 seconds) - For quick integration testing
# cron-tasks:
#   - id: "realtime_volume_sync"
#     cron: "*/30 * * * * *"  # Every 30 seconds

# 🔄 MEDIUM TESTING (2 minutes) - For load testing
# cron-tasks:
#   - id: "realtime_volume_sync"
#     cron: "0 */2 * * * *"  # Every 2 minutes

# 🐌 SLOW TESTING (5 minutes) - For staging environment
# cron-tasks:
#   - id: "realtime_volume_sync"
#     cron: "0 */5 * * * *"  # Every 5 minutes

# 📊 PRODUCTION (15 minutes) - Default production setting
cron-tasks:
  # Activity Cashback Tasks
  - id: "activity_cashback_processing"
    cron: "0 * * * * *"  # Every minute
  - id: "activity_cashback_monthly_reset"
    cron: "0 0 0 1 * *"  # Monthly at 00:00 UTC on 1st day
  - id: "activity_cashback_cleanup"
    cron: "0 0 2 * * *"  # Daily at 02:00 UTC
  - id: "activity_cashback_community_tasks"
    cron: "0 * * * * *"  # Every minute
  
  # Realtime Volume Sync Tasks
  - id: "realtime_volume_sync"
    cron: "0 */15 * * * *"  # Every 15 minutes - PRODUCTION DEFAULT
    
  # Level Upgrade Tasks (unchanged)
  - id: "level_upgrade"
    cron: "0 0 0 * * *"  # Daily at 00:00 UTC

# Hyperliquid configuration
hyper-liquid:
  system-launch-date: {{ index . "SYSTEM_LAUNCH_DATE" | default "" }}
  api-url: {{ index . "HYPERLIQUID_API_URL" | default "https://api.hyperliquid.xyz" }}
  testnet: {{ index . "HYPERLIQUID_TESTNET" | default "false" }}

# ============================================================================
# TESTING GUIDE:
# ============================================================================
# 
# 1. Choose your testing interval from the options above
# 2. Uncomment the desired cron-tasks section
# 3. Comment out the production section
# 4. Restart the application
# 5. Monitor logs: tail -f logs/app.log | grep "realtime volume sync"
# 6. Check database: SELECT * FROM volume_processing_states;
#
# CRON EXPRESSION FORMAT:
# ┌───────────── second (0-59)
# │ ┌─────────── minute (0-59)  
# │ │ ┌───────── hour (0-23)
# │ │ │ ┌─────── day of month (1-31)
# │ │ │ │ ┌───── month (1-12)
# │ │ │ │ │ ┌─── day of week (0-6) (Sunday=0)
# │ │ │ │ │ │
# * * * * * *
#
# EXAMPLES:
# "*/10 * * * * *"  = Every 10 seconds
# "*/30 * * * * *"  = Every 30 seconds  
# "0 * * * * *"     = Every minute
# "0 */2 * * * *"   = Every 2 minutes
# "0 */5 * * * *"   = Every 5 minutes
# "0 */15 * * * *"  = Every 15 minutes (production)
#
# WARNING: Very short intervals (< 30 seconds) should only be used for 
# development testing as they can overload the system!
# ============================================================================
