# Realtime Volume Sync Task

## Overview

The **Realtime Volume Sync Task** (`RealtimeVolumeSyncTask`) provides configurable interval volume updates to bridge the gap between daily volume aggregation and real-time Activity Cashback requirements.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    VOLUME TRACKING SYSTEM                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📊 LevelUpgradeTask (Daily 00:00 UTC)                     │
│  ├─ Source: daily_meme_volumes table                       │
│  ├─ Target: user_tier_info.trading_volume_usd              │
│  ├─ Method: OVERWRITE with daily aggregation               │
│  └─ Purpose: Agent level calculation                       │
│                                                             │
│  ⚡ RealtimeVolumeSyncTask (Configurable Interval)          │
│  ├─ Source: affiliate_transactions table                   │
│  ├─ Target: user_tier_info.accumulated_volume_usd          │
│  ├─ Method: INCREMENTAL addition                           │
│  └─ Purpose: Real-time Activity Cashback                   │
│                                                             │
│  🎯 Activity Cashback System                               │
│  ├─ Uses: GetEffectiveVolume()                             │
│  ├─ Priority: accumulated_volume_usd > trading_volume_usd  │
│  └─ Result: Near real-time task completion                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Configuration

### Production Configuration (Default)
```yaml
# config.yaml
cron-tasks:
  - id: "realtime_volume_sync"
    cron: "0 */15 * * * *"  # Every 15 minutes
```

### Testing Configurations

#### Fast Testing (Every 30 seconds)
```yaml
cron-tasks:
  - id: "realtime_volume_sync"
    cron: "*/30 * * * * *"  # Every 30 seconds
```

#### Medium Testing (Every 2 minutes)
```yaml
cron-tasks:
  - id: "realtime_volume_sync"
    cron: "0 */2 * * * *"  # Every 2 minutes
```

#### Slow Testing (Every 5 minutes)
```yaml
cron-tasks:
  - id: "realtime_volume_sync"
    cron: "0 */5 * * * *"  # Every 5 minutes
```

## Cron Expression Format

```
┌───────────── second (0-59)
│ ┌─────────── minute (0-59)
│ │ ┌───────── hour (0-23)
│ │ │ ┌─────── day of month (1-31)
│ │ │ │ ┌───── month (1-12)
│ │ │ │ │ ┌─── day of week (0-6) (Sunday=0)
│ │ │ │ │ │
* * * * * *
```

### Common Testing Patterns

| Interval | Cron Expression | Use Case |
|----------|----------------|----------|
| 10 seconds | `*/10 * * * * *` | Rapid development testing |
| 30 seconds | `*/30 * * * * *` | Quick integration testing |
| 1 minute | `0 * * * * *` | Basic functionality testing |
| 2 minutes | `0 */2 * * * *` | Load testing |
| 5 minutes | `0 */5 * * * *` | Staging environment |
| 15 minutes | `0 */15 * * * *` | Production (default) |

## Key Features

### 1. **Configurable Intervals**
- Easy to change sync frequency via config
- No code changes required for different intervals
- Suitable for various testing scenarios

### 2. **Incremental Processing**
- Only processes new transactions since last run
- Timestamp-based tracking prevents duplicates
- Efficient database queries with proper indexing

### 3. **Conflict Resolution**
- Separate columns prevent data overwrites
- `accumulated_volume_usd` for real-time sync
- `trading_volume_usd` for daily aggregation

### 4. **Effective Volume Logic**
```go
func (uti *UserTierInfo) GetEffectiveVolume() decimal.Decimal {
    // Prefer real-time data if available
    if uti.AccumulatedVolumeUSD.GreaterThan(decimal.Zero) {
        return uti.AccumulatedVolumeUSD
    }
    // Fallback to daily aggregation
    return uti.TradingVolumeUSD
}
```

### 5. **Comprehensive Audit Trail**
- `volume_processing_states` table tracks last processed timestamp
- `user_volume_update_logs` table logs every volume update
- Detailed logging for debugging and monitoring

## Database Schema

### Volume Processing States
```sql
CREATE TABLE volume_processing_states (
    id SERIAL PRIMARY KEY,
    processor_name VARCHAR(255) UNIQUE NOT NULL,
    last_processed_at TIMESTAMP NOT NULL,
    last_run_at TIMESTAMP NOT NULL,
    processed_transactions BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### User Volume Update Logs
```sql
CREATE TABLE user_volume_update_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    processor_name VARCHAR(255) NOT NULL,
    previous_volume_usd NUMERIC(38,2) NOT NULL,
    added_volume_usd NUMERIC(38,2) NOT NULL,
    new_volume_usd NUMERIC(38,2) NOT NULL,
    transaction_count INTEGER NOT NULL,
    processed_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Testing Guide

### 1. **Change Sync Interval**
Edit `config.yaml`:
```yaml
cron-tasks:
  - id: "realtime_volume_sync"
    cron: "*/30 * * * * *"  # 30 seconds for testing
```

### 2. **Restart Application**
```bash
# Stop current instance
# Update config.yaml
# Start application
./scripts/run.sh local run-scheduler
```

### 3. **Monitor Logs**
```bash
# Watch for sync activity
tail -f logs/app.log | grep "realtime volume sync"
```

### 4. **Verify Database**
```sql
-- Check processing state
SELECT * FROM volume_processing_states 
WHERE processor_name = 'realtime_volume_sync';

-- Check recent updates
SELECT * FROM user_volume_update_logs 
WHERE processor_name = 'realtime_volume_sync'
ORDER BY processed_at DESC LIMIT 10;

-- Check user volumes
SELECT user_id, trading_volume_usd, accumulated_volume_usd 
FROM user_tier_info 
WHERE accumulated_volume_usd > 0;
```

## Performance Considerations

### Production Recommendations
- **15-minute intervals**: Balance between real-time and system load
- **Proper indexing**: Ensure `affiliate_transactions.created_at` is indexed
- **Monitoring**: Track processing time and transaction counts

### Testing Recommendations
- **Start with longer intervals**: 2-5 minutes for initial testing
- **Gradually decrease**: Move to 30 seconds only for rapid development
- **Monitor system load**: Watch CPU and database performance
- **Reset test data**: Clear processing states between test runs

## Troubleshooting

### Common Issues

1. **Task not running**
   - Check cron expression syntax
   - Verify task registration in logs
   - Ensure database connectivity

2. **No volume updates**
   - Check if transactions exist in `affiliate_transactions`
   - Verify transaction status is 'Completed'
   - Check processing state timestamp

3. **Duplicate processing**
   - Verify unique constraint on `processor_name`
   - Check timestamp comparison logic
   - Review transaction boundaries

### Debug Commands
```bash
# Check task registration
grep "realtime_volume_sync" logs/app.log

# Check processing activity
grep "Starting realtime volume sync" logs/app.log

# Check errors
grep "ERROR.*volume" logs/app.log
```

## Migration from Old System

The old `VolumeUpdateTask` with hardcoded 15-minute intervals has been refactored to `RealtimeVolumeSyncTask` with configurable intervals. All functionality remains the same, but with improved flexibility for testing and deployment.

### Key Changes
- `VolumeUpdateTask` → `RealtimeVolumeSyncTask`
- `volume_update_15min` → `realtime_volume_sync`
- `ProcessorName15Min` → `ProcessorName`
- `UpdateTradingVolumes()` → `SyncTradingVolumes()`

This refactoring maintains backward compatibility while providing the flexibility needed for various testing scenarios and future deployment requirements.
