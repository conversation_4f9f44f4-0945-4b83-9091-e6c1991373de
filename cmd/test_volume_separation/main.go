package main

import (
	"fmt"
	"log"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func main() {
	// Initialize the application
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	if global.GVA_DB == nil {
		log.Fatal("Failed to connect to database")
	}

	fmt.Println("=== Testing Volume Column Separation ===")

	// Test 1: Check if accumulated_volume_usd column exists
	fmt.Println("\n1. Checking database schema...")

	// Check if the column exists by trying to query it
	var count int64
	err := global.GVA_DB.Model(&model.UserTierInfo{}).Count(&count).Error
	if err != nil {
		log.Printf("Error querying user_tier_info: %v", err)
		return
	}
	fmt.Printf("Found %d records in user_tier_info table\n", count)

	// Test 2: Check sample data structure
	fmt.Println("\n2. Checking sample user tier info structure...")
	var sampleUsers []model.UserTierInfo
	err = global.GVA_DB.Limit(3).Find(&sampleUsers).Error
	if err != nil {
		log.Printf("Error getting sample users: %v", err)
	} else {
		for _, user := range sampleUsers {
			fmt.Printf("User %s:\n", user.UserID.String())
			fmt.Printf("  - TradingVolumeUSD (LevelUpgradeTask): %s\n", user.TradingVolumeUSD.String())
			fmt.Printf("  - AccumulatedVolumeUSD (VolumeUpdateTask): %s\n", user.AccumulatedVolumeUSD.String())
			fmt.Printf("  - EffectiveVolume (Activity Cashback): %s\n", user.GetEffectiveVolume().String())
			fmt.Println()
		}
	}

	// Test 3: Check processing states
	fmt.Println("\n3. Checking volume processing states...")
	var processingStates []model.VolumeProcessingState
	err = global.GVA_DB.Find(&processingStates).Error
	if err != nil {
		log.Printf("Error getting processing states: %v", err)
	} else {
		fmt.Printf("Found %d processing states:\n", len(processingStates))
		for _, state := range processingStates {
			fmt.Printf("  - Processor: %s\n", state.ProcessorName)
			fmt.Printf("    Last processed: %v\n", state.LastProcessedAt)
			fmt.Printf("    Last run: %v\n", state.LastRunAt)
			fmt.Printf("    Processed transactions: %d\n", state.ProcessedTransactions)
			fmt.Println()
		}
	}

	// Test 4: Check update logs
	fmt.Println("\n4. Checking volume update logs...")
	var updateLogs []model.UserVolumeUpdateLog
	err = global.GVA_DB.Order("processed_at DESC").Limit(5).Find(&updateLogs).Error
	if err != nil {
		log.Printf("Error getting update logs: %v", err)
	} else {
		fmt.Printf("Found %d recent update logs:\n", len(updateLogs))
		for _, log := range updateLogs {
			fmt.Printf("  - User: %s, Processor: %s\n", log.UserID.String(), log.ProcessorName)
			fmt.Printf("    Previous: %s, Added: %s, New: %s\n",
				log.PreviousVolumeUSD.String(),
				log.AddedVolumeUSD.String(),
				log.NewVolumeUSD.String())
			fmt.Printf("    Processed at: %v\n", log.ProcessedAt)
			fmt.Println()
		}
	}

	// Test 5: Validate GetEffectiveVolume logic
	fmt.Println("\n5. Testing GetEffectiveVolume logic...")

	// Create test scenarios
	testCases := []struct {
		name              string
		tradingVolume     string
		accumulatedVolume string
		expectedEffective string
	}{
		{"Both zero", "0", "0", "0"},
		{"Only trading volume", "100.50", "0", "100.50"},
		{"Only accumulated volume", "0", "75.25", "75.25"},
		{"Both present - accumulated preferred", "100.50", "150.75", "150.75"},
	}

	for _, tc := range testCases {
		fmt.Printf("Test case: %s\n", tc.name)

		// Create test user tier info
		userTierInfo := model.UserTierInfo{}
		userTierInfo.TradingVolumeUSD, _ = decimal.NewFromString(tc.tradingVolume)
		userTierInfo.AccumulatedVolumeUSD, _ = decimal.NewFromString(tc.accumulatedVolume)

		effective := userTierInfo.GetEffectiveVolume()
		fmt.Printf("  Expected: %s, Got: %s", tc.expectedEffective, effective.String())

		if effective.String() == tc.expectedEffective {
			fmt.Printf(" ✅\n")
		} else {
			fmt.Printf(" ❌\n")
		}
	}

	fmt.Println("\n=== Volume Separation Test Completed ===")
	fmt.Println("\n📋 Summary:")
	fmt.Println("✅ TradingVolumeUSD: Used by LevelUpgradeTask (daily aggregation)")
	fmt.Println("✅ AccumulatedVolumeUSD: Used by RealtimeVolumeSyncTask (configurable interval)")
	fmt.Println("✅ GetEffectiveVolume(): Used by Activity Cashback (prefers real-time data)")
	fmt.Println("✅ No more conflicts between tasks!")
}
