package model

import (
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestUserTierInfo_GetEffectiveVolume(t *testing.T) {
	tests := []struct {
		name                 string
		tradingVolumeUSD     string
		accumulatedVolumeUSD string
		expectedEffective    string
		description          string
	}{
		{
			name:                 "Both volumes are zero",
			tradingVolumeUSD:     "0",
			accumulatedVolumeUSD: "0",
			expectedEffective:    "0",
			description:          "Should return 0 when both volumes are zero",
		},
		{
			name:                 "Only trading volume has value",
			tradingVolumeUSD:     "100.50",
			accumulatedVolumeUSD: "0",
			expectedEffective:    "100.50",
			description:          "Should return trading volume when accumulated volume is zero",
		},
		{
			name:                 "Only accumulated volume has value",
			tradingVolumeUSD:     "0",
			accumulatedVolumeUSD: "75.25",
			expectedEffective:    "75.25",
			description:          "Should return accumulated volume when it has value",
		},
		{
			name:                 "Both volumes have values - accumulated preferred",
			tradingVolumeUSD:     "100.50",
			accumulatedVolumeUSD: "150.75",
			expectedEffective:    "150.75",
			description:          "Should prefer accumulated volume when both have values",
		},
		{
			name:                 "Accumulated volume is smaller but still preferred",
			tradingVolumeUSD:     "200.00",
			accumulatedVolumeUSD: "50.25",
			expectedEffective:    "50.25",
			description:          "Should prefer accumulated volume even if it's smaller",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test user tier info
			userTierInfo := UserTierInfo{
				UserID: uuid.New(),
			}

			// Set volumes from test case
			var err error
			userTierInfo.TradingVolumeUSD, err = decimal.NewFromString(tt.tradingVolumeUSD)
			assert.NoError(t, err, "Should parse trading volume")

			userTierInfo.AccumulatedVolumeUSD, err = decimal.NewFromString(tt.accumulatedVolumeUSD)
			assert.NoError(t, err, "Should parse accumulated volume")

			// Get effective volume
			effectiveVolume := userTierInfo.GetEffectiveVolume()

			// Verify result
			expectedVolume, err := decimal.NewFromString(tt.expectedEffective)
			assert.NoError(t, err, "Should parse expected volume")

			assert.True(t, effectiveVolume.Equal(expectedVolume),
				"Expected %s, got %s. %s",
				tt.expectedEffective,
				effectiveVolume.String(),
				tt.description)

			t.Logf("✅ %s: Expected %s, Got %s",
				tt.description,
				tt.expectedEffective,
				effectiveVolume.String())
		})
	}
}

func TestUserTierInfo_VolumeHelperMethods(t *testing.T) {
	userTierInfo := UserTierInfo{
		UserID:               uuid.New(),
		TradingVolumeUSD:     decimal.NewFromFloat(100.0),
		AccumulatedVolumeUSD: decimal.NewFromFloat(50.0),
	}

	t.Run("AddTradingVolume", func(t *testing.T) {
		initialAccumulated := userTierInfo.AccumulatedVolumeUSD

		userTierInfo.AddTradingVolume(decimal.NewFromFloat(25.0))

		// Should only affect TradingVolumeUSD
		assert.True(t, userTierInfo.TradingVolumeUSD.Equal(decimal.NewFromFloat(125.0)),
			"TradingVolumeUSD should be updated")
		assert.True(t, userTierInfo.AccumulatedVolumeUSD.Equal(initialAccumulated),
			"AccumulatedVolumeUSD should remain unchanged")
	})

	t.Run("AddAccumulatedVolume", func(t *testing.T) {
		initialTrading := userTierInfo.TradingVolumeUSD

		userTierInfo.AddAccumulatedVolume(decimal.NewFromFloat(30.0))

		// Should only affect AccumulatedVolumeUSD
		assert.True(t, userTierInfo.TradingVolumeUSD.Equal(initialTrading),
			"TradingVolumeUSD should remain unchanged")
		assert.True(t, userTierInfo.AccumulatedVolumeUSD.Equal(decimal.NewFromFloat(80.0)),
			"AccumulatedVolumeUSD should be updated")
	})

	t.Run("SetAccumulatedVolume", func(t *testing.T) {
		initialTrading := userTierInfo.TradingVolumeUSD

		userTierInfo.SetAccumulatedVolume(decimal.NewFromFloat(200.0))

		// Should only affect AccumulatedVolumeUSD
		assert.True(t, userTierInfo.TradingVolumeUSD.Equal(initialTrading),
			"TradingVolumeUSD should remain unchanged")
		assert.True(t, userTierInfo.AccumulatedVolumeUSD.Equal(decimal.NewFromFloat(200.0)),
			"AccumulatedVolumeUSD should be set to new value")
	})
}

func TestUserTierInfo_EffectiveVolumeIntegration(t *testing.T) {
	t.Run("Real-world scenario simulation", func(t *testing.T) {
		// Simulate a real-world scenario where:
		// 1. LevelUpgradeTask sets TradingVolumeUSD to daily aggregation
		// 2. VolumeUpdateTask adds to AccumulatedVolumeUSD for real-time updates
		// 3. GetEffectiveVolume should return the most up-to-date value

		userTierInfo := UserTierInfo{
			UserID:               uuid.New(),
			TradingVolumeUSD:     decimal.NewFromFloat(1000.0), // Daily aggregation
			AccumulatedVolumeUSD: decimal.Zero,                 // Initially zero
		}

		// Initially, effective volume should be trading volume
		effectiveVolume := userTierInfo.GetEffectiveVolume()
		assert.True(t, effectiveVolume.Equal(decimal.NewFromFloat(1000.0)),
			"Should use trading volume when accumulated is zero")

		// Simulate VolumeUpdateTask adding real-time volume
		userTierInfo.AddAccumulatedVolume(decimal.NewFromFloat(1150.0))

		// Now effective volume should prefer accumulated volume
		effectiveVolume = userTierInfo.GetEffectiveVolume()
		assert.True(t, effectiveVolume.Equal(decimal.NewFromFloat(1150.0)),
			"Should prefer accumulated volume when available")

		t.Logf("✅ Real-world scenario: Trading=%s, Accumulated=%s, Effective=%s",
			userTierInfo.TradingVolumeUSD.String(),
			userTierInfo.AccumulatedVolumeUSD.String(),
			effectiveVolume.String())
	})
}
