package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskProgressService implements TaskProgressServiceInterface
type TaskProgressService struct {
	progressRepo      activity_cashback.UserTaskProgressRepositoryInterface
	taskRepo          activity_cashback.ActivityTaskRepositoryInterface
	completionFactory *activity_cashback.TaskCompletionRepositoryFactory
}

// NewTaskProgressService creates a new TaskProgressService
func NewTaskProgressService(
	progressRepo activity_cashback.UserTaskProgressRepositoryInterface,
	taskRepo activity_cashback.ActivityTaskRepositoryInterface,
) TaskProgressServiceInterface {
	return &TaskProgressService{
		progressRepo:      progressRepo,
		taskRepo:          taskRepo,
		completionFactory: activity_cashback.NewTaskCompletionRepositoryFactory(),
	}
}

// GetUserTaskProgress retrieves all task progress for a user
func (s *TaskProgressService) GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}
	return progress, nil
}

// GetTaskProgress retrieves specific task progress for a user
func (s *TaskProgressService) GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	progress, err := s.progressRepo.GetByUserAndTask(ctx, userID, taskID)
	if err != nil {
		// Don't wrap gorm.ErrRecordNotFound so it can be detected by callers
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		return nil, fmt.Errorf("failed to get task progress: %w", err)
	}
	return progress, nil
}

// InitializeTaskProgress creates initial progress record for a user task
func (s *TaskProgressService) InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	// Get task to set initial values
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Create initial progress
	progress := &model.UserTaskProgress{
		UserID:        userID,
		TaskID:        taskID,
		Status:        model.TaskStatusNotStarted,
		ProgressValue: 0,
	}

	// Set target value based on task conditions
	if task.Conditions != nil {
		if task.Conditions.RequiredTradeCount != nil {
			progress.TargetValue = task.Conditions.RequiredTradeCount
		} else if task.Conditions.ConsecutiveDays != nil {
			progress.TargetValue = task.Conditions.ConsecutiveDays
		} else if task.Conditions.MinTradingVolume != nil {
			// For accumulated trading tasks, target value should be the milestone amount
			targetVolume := int(*task.Conditions.MinTradingVolume)
			progress.TargetValue = &targetVolume
		}
	}

	// Set default target value if not specified
	if progress.TargetValue == nil {
		defaultTarget := 1
		progress.TargetValue = &defaultTarget
	}

	if err := s.progressRepo.Create(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to create task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress initialized",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	return progress, nil
}

// IncrementProgress increments task progress by a specified amount
func (s *TaskProgressService) IncrementProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress
	progress.ProgressValue += increment
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	// Check if task is completed
	if progress.TargetValue != nil && progress.ProgressValue >= *progress.TargetValue {
		// For automated tasks (like accumulated trading), set status to CLAIMED since points are auto-awarded
		// For manual tasks, this would be COMPLETED until user claims
		progress.Status = model.TaskStatusClaimed
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++

		// Note: pointsEarned should be set by the calling service that handles point awarding
		// This method only handles progress completion, not point calculation
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	return nil
}

// SetProgress sets task progress to a specific value
func (s *TaskProgressService) SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress
	progress.ProgressValue = value
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	// Check if task is completed
	if progress.TargetValue != nil && progress.ProgressValue >= *progress.TargetValue {
		// For automated tasks (like accumulated trading), set status to CLAIMED since points are auto-awarded
		// For manual tasks, this would be COMPLETED until user claims
		progress.Status = model.TaskStatusClaimed
		now := time.Now()
		progress.LastCompletedAt = &now
		progress.CompletionCount++

		// Note: pointsEarned should be set by the calling service that handles point awarding
		// This method only handles progress completion, not point calculation
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	return nil
}

// SetProgressWithoutAutoComplete sets progress value without auto-completion logic
func (s *TaskProgressService) SetProgressWithoutAutoComplete(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Update progress without auto-completion logic
	progress.ProgressValue = value
	progress.Status = model.TaskStatusInProgress
	progress.UpdatedAt = time.Now()

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update task progress: %w", err)
	}

	global.GVA_LOG.Debug("Task progress updated without auto-completion",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("progress_value", value))

	return nil
}

// CompleteProgress marks a task as completed
func (s *TaskProgressService) CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	return s.CompleteProgressWithPoints(ctx, userID, taskID, 0)
}

// CompleteProgressWithPoints marks a task as completed and sets points earned
func (s *TaskProgressService) CompleteProgressWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error {
	return s.completeProgressWithPointsInternal(ctx, userID, taskID, pointsEarned, false)
}

// CompleteProgressiveTaskWithPoints marks a progressive task as completed without overriding progressValue
func (s *TaskProgressService) CompleteProgressiveTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error {
	return s.completeProgressWithPointsInternal(ctx, userID, taskID, pointsEarned, true)
}

// completeProgressWithPointsInternal is the internal implementation
func (s *TaskProgressService) completeProgressWithPointsInternal(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int, isProgressive bool) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Mark as completed and claimed (auto-claim)
	progress.Status = model.TaskStatusClaimed
	now := time.Now()
	progress.LastCompletedAt = &now
	progress.CompletionCount++
	progress.UpdatedAt = now

	// For progressive tasks, don't override progressValue (use streakCount instead)
	// For normal tasks, set progress to target value
	if !isProgressive && progress.TargetValue != nil {
		progress.ProgressValue = *progress.TargetValue
	}

	// Set points earned if provided
	if pointsEarned > 0 {
		progress.PointsEarned = pointsEarned
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to complete task progress: %w", err)
	}

	global.GVA_LOG.Info("Task progress completed",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Bool("is_progressive", isProgressive),
		zap.Int("points_earned", pointsEarned))

	return nil
}

// UpdateStreak updates streak count for consecutive tasks
func (s *TaskProgressService) UpdateStreak(ctx context.Context, userID, taskID uuid.UUID, increment bool) error {
	progress, err := s.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return err
	}

	if increment {
		progress.StreakCount++
	} else {
		progress.StreakCount = 0
	}

	progress.UpdatedAt = time.Now()

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update streak: %w", err)
	}

	global.GVA_LOG.Info("Streak updated",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("streak_count", progress.StreakCount))

	return nil
}

// ResetStreak resets streak count to zero
func (s *TaskProgressService) ResetStreak(ctx context.Context, userID, taskID uuid.UUID) error {
	return s.UpdateStreak(ctx, userID, taskID, false)
}

// GetUserStreaks retrieves all tasks with active streaks for a user
func (s *TaskProgressService) GetUserStreaks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	streaks, err := s.progressRepo.GetStreakTasks(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user streaks: %w", err)
	}
	return streaks, nil
}

// GetCompletionStats retrieves completion statistics for a user within a date range
func (s *TaskProgressService) GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	unifiedRepo := s.completionFactory.GetUnifiedRepository()
	stats, err := unifiedRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get completion stats: %w", err)
	}
	return stats, nil
}

// GetTaskCompletionRate calculates completion rate for a specific task
func (s *TaskProgressService) GetTaskCompletionRate(ctx context.Context, taskID uuid.UUID) (float64, error) {
	// Get total users who have this task
	allProgress, err := s.progressRepo.GetByTaskID(ctx, taskID)
	if err != nil {
		return 0, fmt.Errorf("failed to get task progress: %w", err)
	}

	if len(allProgress) == 0 {
		return 0, nil
	}

	// Count completed tasks
	completedCount := 0
	for _, progress := range allProgress {
		if progress.IsCompleted() {
			completedCount++
		}
	}

	completionRate := float64(completedCount) / float64(len(allProgress)) * 100
	return completionRate, nil
}

// CompleteTaskProgressOnly completes task progress only without awarding points
// Note: This is a placeholder implementation. The actual point awarding will be handled
// by TaskManagementService since TaskProgressService doesn't have access to TierService
func (s *TaskProgressService) CompleteTaskProgressOnly(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task to determine points
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Check if task can be completed (for daily tasks, check if already completed today)
	if task.Frequency == model.FrequencyDaily {
		progress, err := s.GetTaskProgress(ctx, userID, taskID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to get task progress: %w", err)
		}

		if progress != nil && progress.LastCompletedAt != nil {
			today := time.Now().Truncate(24 * time.Hour)
			lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
			if today.Equal(lastCompleted) {
				global.GVA_LOG.Debug("Task already completed today",
					zap.String("user_id", userID.String()),
					zap.String("task_id", taskID.String()),
					zap.Time("last_completed", *progress.LastCompletedAt))
				return nil // Already completed today, no error
			}
		}
	}

	// Complete the progress
	if err := s.CompleteProgress(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to complete progress: %w", err)
	}

	// Note: Point awarding will be handled by the calling service
	global.GVA_LOG.Info("Task progress completed via CompleteTaskProgressOnly",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points", task.Points))

	return nil
}
