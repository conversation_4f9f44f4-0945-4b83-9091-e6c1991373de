package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TaskManagementServiceInterface defines the interface for task management operations
type TaskManagementServiceInterface interface {
	// Task CRUD operations
	CreateTask(ctx context.Context, task *model.ActivityTask) error
	UpdateTask(ctx context.Context, task *model.ActivityTask) error
	DeleteTask(ctx context.Context, taskID uuid.UUID) error
	GetTaskByID(ctx context.Context, taskID uuid.UUID) (*model.ActivityTask, error)
	GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error)
	GetTasksByCategory(ctx context.Context, categoryName model.TaskCategoryName) ([]model.ActivityTask, error)

	// Task completion and progress
	CompleteTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error
	CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error
	UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, progressValue int) error

	// Task refresh and reset
	RefreshUserTasks(ctx context.Context, userID uuid.UUID) error
	ResetDailyTasks(ctx context.Context) error
	ResetWeeklyTasks(ctx context.Context) error
	ResetMonthlyTasks(ctx context.Context) error

	// Task verification
	VerifyTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (bool, error)
	VerifySocialMediaTask(ctx context.Context, userID, taskID uuid.UUID, socialData map[string]interface{}) (bool, error)
	VerifyTradingTask(ctx context.Context, userID, taskID uuid.UUID, tradingData map[string]interface{}) (bool, error)

	// Pending community task operations
	CreatePendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (*model.PendingCommunityTask, error)
	GetPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error)
	HasPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error)
	ProcessPendingCommunityTasks(ctx context.Context) error
	ProcessSinglePendingTask(ctx context.Context, pendingTask *model.PendingCommunityTask) error

	// Consecutive check-in task operations
	GetVisibleConsecutiveCheckinTask(ctx context.Context, userID uuid.UUID) (*model.ActivityTask, error)
}

// TierManagementServiceInterface defines the interface for tier management operations
type TierManagementServiceInterface interface {
	// User tier operations
	GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error)
	CreateUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error)
	UpdateUserTierInfo(ctx context.Context, tierInfo *model.UserTierInfo) error

	// Points management
	AddPoints(ctx context.Context, userID uuid.UUID, points int, source string) error
	GetUserPoints(ctx context.Context, userID uuid.UUID) (int, error)
	GetUserRank(ctx context.Context, userID uuid.UUID) (int, error)

	// Tier progression
	CheckTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error)
	UpgradeUserTier(ctx context.Context, userID uuid.UUID, newTier int) error
	GetNextTierRequirement(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, int, error)
	TriggerTierUpgradeCheck(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error)

	// Cashback management
	AddCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error
	GetClaimableCashback(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	ClaimCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error

	// Trading volume management
	AddTradingVolume(ctx context.Context, userID uuid.UUID, volume decimal.Decimal) error

	// Monthly stats
	UpdateActivity(ctx context.Context, userID uuid.UUID) error
	ResetMonthlyStats(ctx context.Context) error
	GetMonthlyStats(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error)
}

// TaskProgressServiceInterface defines the interface for task progress operations
type TaskProgressServiceInterface interface {
	// Progress tracking
	GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)

	GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error)
	InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error)

	// Progress updates
	IncrementProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error
	SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error
	CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error
	CompleteProgressWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error
	CompleteProgressiveTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, pointsEarned int) error

	// Streak management
	UpdateStreak(ctx context.Context, userID, taskID uuid.UUID, increment bool) error
	ResetStreak(ctx context.Context, userID, taskID uuid.UUID) error
	GetUserStreaks(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)

	// Progress analytics
	GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionRate(ctx context.Context, taskID uuid.UUID) (float64, error)

	// Combined operations (deprecated - use TaskManagementService.CompleteTaskWithPoints instead)
	CompleteTaskProgressOnly(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error
}

// CashbackClaimServiceInterface defines the interface for cashback claim operations
type CashbackClaimServiceInterface interface {
	// Claim operations
	CreateClaim(ctx context.Context, userID uuid.UUID, claimType model.ClaimType, amountUSD, amountSOL decimal.Decimal, metadata *model.ClaimMetadata) (*model.ActivityCashbackClaim, error)
	ProcessClaim(ctx context.Context, claimID uuid.UUID) error
	CompleteClaim(ctx context.Context, claimID uuid.UUID, transactionHash string) error
	FailClaim(ctx context.Context, claimID uuid.UUID, errorDetails map[string]interface{}) error

	// Claim queries
	GetUserClaims(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error)
	GetClaimByID(ctx context.Context, claimID uuid.UUID) (*model.ActivityCashbackClaim, error)
	GetPendingClaims(ctx context.Context) ([]model.ActivityCashbackClaim, error)

	// Claim analytics
	GetUserClaimHistory(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) ([]model.ActivityCashbackClaim, error)
	GetTotalClaimedAmount(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetClaimStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)
}

// ActivityCashbackServiceInterface defines the main interface for activity cashback system
type ActivityCashbackServiceInterface interface {
	TaskManagementServiceInterface
	TierManagementServiceInterface
	TaskProgressServiceInterface
	CashbackClaimServiceInterface

	// System operations
	InitializeUserForActivityCashback(ctx context.Context, userID uuid.UUID) error
	GetUserDashboard(ctx context.Context, userID uuid.UUID) (*UserDashboard, error)
	GetTaskCenter(ctx context.Context, userID uuid.UUID) (*TaskCenter, error)
	GetActivityCashbackSummary(ctx context.Context, userID uuid.UUID) (*ActivityCashbackSummary, error)
	GetUserTaskListByCategoryWithDetails(ctx context.Context, userID uuid.UUID, categoryName model.TaskCategoryName) ([]TaskWithProgress, error)
	RefreshTaskList(ctx context.Context, userID uuid.UUID) error

	// Override to ensure proper delegation to TaskManagementService
	CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error

	// Task registry operations
	ProcessTaskWithRegistry(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error
}

// UserDashboard represents the user dashboard data
type UserDashboard struct {
	UserTierInfo      *model.UserTierInfo           `json:"user_tier_info"`
	TierBenefit       *model.TierBenefit            `json:"tier_benefit"`
	NextTier          *model.TierBenefit            `json:"next_tier,omitempty"`
	PointsToNextTier  int                           `json:"points_to_next_tier"`
	ClaimableCashback decimal.Decimal               `json:"claimable_cashback"`
	RecentClaims      []model.ActivityCashbackClaim `json:"recent_claims"`
	UserRank          int                           `json:"user_rank"`
}

// TaskCenter represents the task center data
type TaskCenter struct {
	Categories        []TaskCategoryWithTasks  `json:"categories"`
	UserProgress      []model.UserTaskProgress `json:"user_progress"`
	CompletedToday    int                      `json:"completed_today"`
	PointsEarnedToday int                      `json:"points_earned_today"`
	StreakTasks       []model.UserTaskProgress `json:"streak_tasks"`
}

// TaskCategoryWithTasks represents a category with its tasks and user progress
type TaskCategoryWithTasks struct {
	Category model.TaskCategory `json:"category"`
	Tasks    []TaskWithProgress `json:"tasks"`
}

// TaskWithProgress represents a task with user progress
type TaskWithProgress struct {
	Task     model.ActivityTask      `json:"task"`
	Progress *model.UserTaskProgress `json:"progress,omitempty"`
}

// ActivityCashbackSummary represents optimized summary data for frontend UI
type ActivityCashbackSummary struct {
	// Current ranking info
	CurrentLevel     int     `json:"current_level"`
	CurrentLevelName string  `json:"current_level_name"`
	NextLevel        *int    `json:"next_level,omitempty"`
	NextLevelName    *string `json:"next_level_name,omitempty"`

	// Progress calculation
	CurrentScore           int     `json:"current_score"`
	TotalScoreForNextLevel *int    `json:"total_score_for_next_level,omitempty"`
	ScoreRequiredToUpgrade *int    `json:"score_required_to_upgrade,omitempty"`
	ProgressPercentage     float64 `json:"progress_percentage"`

	// Trading volume (MEME only for now)
	AccumulatedTradingVolumeUSD decimal.Decimal `json:"accumulated_trading_volume_usd"`

	// Activity tracking
	ActiveLogonDays int `json:"active_logon_days"`

	// Cashback information
	AccumulatedCashbackUSD decimal.Decimal `json:"accumulated_cashback_usd"`
	ClaimableCashbackUSD   decimal.Decimal `json:"claimable_cashback_usd"`
	ClaimedCashbackUSD     decimal.Decimal `json:"claimed_cashback_usd"`

	// Additional tier info
	CurrentTierColor *string `json:"current_tier_color,omitempty"`
	CurrentTierIcon  *string `json:"current_tier_icon,omitempty"`
	NextTierColor    *string `json:"next_tier_color,omitempty"`
	NextTierIcon     *string `json:"next_tier_icon,omitempty"`
}
